
2025-07-03 22:43:15 worker-1    | {"level": "info", "event": "Initializing custom MCP https://mcp.composio.dev/composio/server/68d33e3f-af97-45ae-9a36-7870b4618dc8/mcp?user_id=fd8c1084-45ca-449a-9fc8-3b94ab53c239 with HTTP type", "lineno": 286, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:15.664367Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | Connected via HTTP (15 tools)
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_DELETE_DRAFT", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.514282Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_DELETE_MESSAGE", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.515090Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_FETCH_EMAILS", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.515248Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_GET_CONTACTS", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.515550Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_LIST_DRAFTS", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.515947Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_MOVE_TO_TRASH", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.516435Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_PATCH_LABEL", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.516895Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_REPLY_TO_THREAD", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.517989Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_SEARCH_PEOPLE", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.518340Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_SEND_DRAFT", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.518550Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_SEND_EMAIL", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.518744Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.519162Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_FETCH_MESSAGE_BY_THREAD_ID", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.521196Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "debug", "event": "Registered custom tool: custom_gmail_GMAIL_LIST_THREADS", "lineno": 311, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.522195Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "info", "event": "Successfully initialized custom MCP Gmail with 14 tools", "lineno": 313, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.525740Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "info", "event": "Initializing custom MCP: {'name': 'Google Sheets', 'qualifiedName': 'custom_http_google_sheets', 'config': {'url': 'https://mcp.composio.dev/composio/server/3cb65ed0-c88b-45b5-a140-7d81609136d2/mcp?user_id=fd8c1084-45ca-449a-9fc8-3b94ab53c239'}, 'enabledTools': ['GOOGLE_SHEETS_ADD_SHEET_TO_SPREADSHEET', 'GOOGLE_SHEETS_CLEAR_BASIC_FILTER', 'GOOGLE_SHEETS_DELETE_SHEET', 'GOOGLE_SHEETS_GET_SPREADSHEET_BY_DATA_FILTER', 'GOOGLE_SHEETS_GET_SPREADSHEET_INFO', 'GOOGLE_SHEETS_INSERT_DIMENSION', 'GOOGLE_SHEETS_SET_BASIC_FILTER', 'GOOGLE_SHEETS_APPEND_VALUES', 'GOOGLE_SHEETS_BATCH_CLEAR_VALUES', 'GOOGLE_SHEETS_BATCH_GET_VALUES_BY_DATA_FILTER', 'GOOGLE_SHEETS_UPDATE_SPREADSHEET_PROPERTIES', 'GOOGLE_SHEETS_BATCH_GET_SPREADSHEET', 'GOOGLE_SHEETS_BATCH_UPDATE_SPREADSHEET', 'GOOGLE_SHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER', 'GOOGLE_SHEETS_CLEAR_VALUES', 'GOOGLE_SHEETS_CREATE_SPREADSHEET', 'GOOGLE_SHEETS_FIND_WORKSHEET_BY_TITLE', 'GOOGLE_SHEETS_FORMAT_CELL', 'GOOGLE_SHEETS_SEARCH_SPREADSHEETS'], 'instructions': '', 'isCustom': True, 'customType': 'http'}", "lineno": 209, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.526241Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "info", "event": "Initializing custom MCP: Google Sheets (type: http)", "lineno": 215, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.526507Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:17 worker-1    | {"level": "info", "event": "Initializing custom MCP https://mcp.composio.dev/composio/server/3cb65ed0-c88b-45b5-a140-7d81609136d2/mcp?user_id=fd8c1084-45ca-449a-9fc8-3b94ab53c239 with HTTP type", "lineno": 286, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:17.527258Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | Connected via HTTP (19 tools)
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Successfully initialized custom MCP Google Sheets with 0 tools", "lineno": 313, "func_name": "_initialize_custom_mcps", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.308183Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "MCPManager returned 0 tools", "lineno": 419, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.308637Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing 14 custom MCP tools", "lineno": 429, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.308763Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_DELETE_DRAFT", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.308828Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_DELETE_DRAFT': clean_tool_name='GMAIL_DELETE_DRAFT', method_name='GMAIL_DELETE_DRAFT', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.308968Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_DELETE_DRAFT' for MCP tool 'custom_gmail_GMAIL_DELETE_DRAFT' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.309380Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_DELETE_MESSAGE", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.309506Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_DELETE_MESSAGE': clean_tool_name='GMAIL_DELETE_MESSAGE', method_name='GMAIL_DELETE_MESSAGE', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.309901Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_DELETE_MESSAGE' for MCP tool 'custom_gmail_GMAIL_DELETE_MESSAGE' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.310219Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_FETCH_EMAILS", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.310315Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_FETCH_EMAILS': clean_tool_name='GMAIL_FETCH_EMAILS', method_name='GMAIL_FETCH_EMAILS', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.310642Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_FETCH_EMAILS' for MCP tool 'custom_gmail_GMAIL_FETCH_EMAILS' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.311056Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_GET_CONTACTS", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.311266Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_GET_CONTACTS': clean_tool_name='GMAIL_GET_CONTACTS', method_name='GMAIL_GET_CONTACTS', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.311384Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_GET_CONTACTS' for MCP tool 'custom_gmail_GMAIL_GET_CONTACTS' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.311651Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_LIST_DRAFTS", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.311725Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_LIST_DRAFTS': clean_tool_name='GMAIL_LIST_DRAFTS', method_name='GMAIL_LIST_DRAFTS', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.311782Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_LIST_DRAFTS' for MCP tool 'custom_gmail_GMAIL_LIST_DRAFTS' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.312246Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_MOVE_TO_TRASH", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.312350Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_MOVE_TO_TRASH': clean_tool_name='GMAIL_MOVE_TO_TRASH', method_name='GMAIL_MOVE_TO_TRASH', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.312678Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_MOVE_TO_TRASH' for MCP tool 'custom_gmail_GMAIL_MOVE_TO_TRASH' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.313600Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_PATCH_LABEL", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.313788Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_PATCH_LABEL': clean_tool_name='GMAIL_PATCH_LABEL', method_name='GMAIL_PATCH_LABEL', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.313845Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_PATCH_LABEL' for MCP tool 'custom_gmail_GMAIL_PATCH_LABEL' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.313933Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_REPLY_TO_THREAD", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.314018Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_REPLY_TO_THREAD': clean_tool_name='GMAIL_REPLY_TO_THREAD', method_name='GMAIL_REPLY_TO_THREAD', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.314339Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_REPLY_TO_THREAD' for MCP tool 'custom_gmail_GMAIL_REPLY_TO_THREAD' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.314701Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_SEARCH_PEOPLE", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315127Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_SEARCH_PEOPLE': clean_tool_name='GMAIL_SEARCH_PEOPLE', method_name='GMAIL_SEARCH_PEOPLE', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315280Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_SEARCH_PEOPLE' for MCP tool 'custom_gmail_GMAIL_SEARCH_PEOPLE' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315474Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_SEND_DRAFT", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315566Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_SEND_DRAFT': clean_tool_name='GMAIL_SEND_DRAFT', method_name='GMAIL_SEND_DRAFT', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315666Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_SEND_DRAFT' for MCP tool 'custom_gmail_GMAIL_SEND_DRAFT' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315761Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_SEND_EMAIL", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315821Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_SEND_EMAIL': clean_tool_name='GMAIL_SEND_EMAIL', method_name='GMAIL_SEND_EMAIL', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.315902Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_SEND_EMAIL' for MCP tool 'custom_gmail_GMAIL_SEND_EMAIL' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.316440Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.316585Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID': clean_tool_name='GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID', method_name='GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.316686Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID' for MCP tool 'custom_gmail_GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.317006Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_FETCH_MESSAGE_BY_THREAD_ID", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.317477Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_FETCH_MESSAGE_BY_THREAD_ID': clean_tool_name='GMAIL_FETCH_MESSAGE_BY_THREAD_ID', method_name='GMAIL_FETCH_MESSAGE_BY_THREAD_ID', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.317806Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_FETCH_MESSAGE_BY_THREAD_ID' for MCP tool 'custom_gmail_GMAIL_FETCH_MESSAGE_BY_THREAD_ID' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.317967Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Processing custom tool: custom_gmail_GMAIL_LIST_THREADS", "lineno": 431, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.318068Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Creating dynamic method for tool 'custom_gmail_GMAIL_LIST_THREADS': clean_tool_name='GMAIL_LIST_THREADS', method_name='GMAIL_LIST_THREADS', server='Gmail'", "lineno": 466, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.318362Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "debug", "event": "Created dynamic method 'GMAIL_LIST_THREADS' for MCP tool 'custom_gmail_GMAIL_LIST_THREADS' from server 'Gmail'", "lineno": 523, "func_name": "_create_dynamic_method", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.318506Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Created 14 dynamic MCP tool methods", "lineno": 440, "func_name": "_create_dynamic_tools", "filename": "mcp_tool_wrapper.py", "timestamp": "2025-07-04T02:43:19.318581Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "MCP tools initialized successfully", "lineno": 337, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.318893Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "MCP wrapper has 15 schemas available", "lineno": 339, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.319010Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_DELETE_DRAFT", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.319464Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_DELETE_MESSAGE", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.319645Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_FETCH_EMAILS", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.319789Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_GET_CONTACTS", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.319857Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_LIST_DRAFTS", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.319921Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_MOVE_TO_TRASH", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.319977Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_PATCH_LABEL", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.320147Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_REPLY_TO_THREAD", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.320220Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_SEARCH_PEOPLE", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.320299Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_SEND_DRAFT", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.320412Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_SEND_EMAIL", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.320521Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.320591Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_FETCH_MESSAGE_BY_THREAD_ID", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.321400Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Registered dynamic MCP tool: GMAIL_LIST_THREADS", "lineno": 350, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.321562Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "All registered tools after MCP initialization: ['expand_message', 'ask', 'complete', 'web_browser_takeover', 'call_mcp_tool', 'GMAIL_DELETE_DRAFT', 'GMAIL_DELETE_MESSAGE', 'GMAIL_FETCH_EMAILS', 'GMAIL_GET_CONTACTS', 'GMAIL_LIST_DRAFTS', 'GMAIL_MOVE_TO_TRASH', 'GMAIL_PATCH_LABEL', 'GMAIL_REPLY_TO_THREAD', 'GMAIL_SEARCH_PEOPLE', 'GMAIL_SEND_DRAFT', 'GMAIL_SEND_EMAIL', 'GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID', 'GMAIL_FETCH_MESSAGE_BY_THREAD_ID', 'GMAIL_LIST_THREADS']", "lineno": 356, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.321740Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "MCP tools registered: ['expand_message', 'ask', 'complete', 'web_browser_takeover', 'GMAIL_DELETE_DRAFT', 'GMAIL_DELETE_MESSAGE', 'GMAIL_FETCH_EMAILS', 'GMAIL_GET_CONTACTS', 'GMAIL_LIST_DRAFTS', 'GMAIL_MOVE_TO_TRASH', 'GMAIL_PATCH_LABEL', 'GMAIL_REPLY_TO_THREAD', 'GMAIL_SEARCH_PEOPLE', 'GMAIL_SEND_DRAFT', 'GMAIL_SEND_EMAIL', 'GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID', 'GMAIL_FETCH_MESSAGE_BY_THREAD_ID', 'GMAIL_LIST_THREADS']", "lineno": 379, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.321942Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Using standard system prompt", "lineno": 407, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.322234Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Using ONLY custom agent system prompt for: Default Agent", "lineno": 430, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.322374Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udee0\ufe0f  FINAL TOOL REGISTRY SUMMARY:", "lineno": 545, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.342528Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "  \u251c\u2500 Total tools registered: 19", "lineno": 546, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.342773Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "  \u251c\u2500 AgentPress tools: 0 (None)", "lineno": 597, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.342924Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "  \u251c\u2500 MCP tools: 18 (expand_message, ask, complete... if mcp_tools else 'None')", "lineno": 600, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.343018Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "  \u2514\u2500 System tools: 1 (call_mcp_tool)", "lineno": 603, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.343098Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Querying for latest user message in thread: 151a60bb-84eb-4a14-8afc-b40f4423b338", "lineno": 611, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.343170Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d User message query completed. Found 1 messages", "lineno": 621, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.423987Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | 🔍🔍🔍 TOOL MENTION PREPROCESSING STARTING
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Starting tool mention preprocessing...", "lineno": 629, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.424239Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Latest user message query result: data=[{'message_id': '048c90e7-2707-4e5f-b151-537b5f2a1da6', 'thread_id': '151a60bb-84eb-4a14-8afc-b40f4423b338', 'type': 'user', 'is_llm_message': True, 'content': '{\"role\": \"user\", \"content\": \"create a google sheet with the following data a b c d 1 2 3 4 Create this file and email the <NAME_EMAIL>\\\\r\\\\nAtlas Static LogoAtlas Animated Logo\\\\r\\\\n\"}', 'metadata': {}, 'created_at': '2025-07-04T02:43:14.533772+00:00', 'updated_at': '2025-07-04T02:43:14.632441+00:00', 'agent_id': None, 'agent_version_id': None}] count=None", "lineno": 630, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.424399Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Latest user message data: [{'message_id': '048c90e7-2707-4e5f-b151-537b5f2a1da6', 'thread_id': '151a60bb-84eb-4a14-8afc-b40f4423b338', 'type': 'user', 'is_llm_message': True, 'content': '{\"role\": \"user\", \"content\": \"create a google sheet with the following data a b c d 1 2 3 4 Create this file and email the <NAME_EMAIL>\\\\r\\\\nAtlas Static LogoAtlas Animated Logo\\\\r\\\\n\"}', 'metadata': {}, 'created_at': '2025-07-04T02:43:14.533772+00:00', 'updated_at': '2025-07-04T02:43:14.632441+00:00', 'agent_id': None, 'agent_version_id': None}]", "lineno": 631, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.424523Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Latest user message data length: 1", "lineno": 634, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.424639Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Raw message data type: <class 'str'>", "lineno": 645, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.610583Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Raw message data: {\"role\": \"user\", \"content\": \"create a google sheet with the following data a b c d 1 2 3 4 Create this file and email the <NAME_EMAIL>\\r\\nAtlas Static LogoAtlas Animated Logo\\r\\n\"}", "lineno": 646, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.611021Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Parsed message data: {'role': 'user', 'content': 'create a google sheet with the following data a b c d 1 2 3 4 Create this file and email the <NAME_EMAIL>\\r\\nAtlas Static LogoAtlas Animated Logo\\r\\n'}", "lineno": 650, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.611461Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd0d Extracted user message content: 'create a google sheet with the following data a b c d 1 2 3 4 Create this file and email the <NAME_EMAIL>\r\nAtlas Static LogoAtlas Animated Logo\r\n'", "lineno": 653, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.611642Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udcdd Checking user message for tool mentions: 'create a google sheet with the following data a b c d 1 2 3 4 Create this file and email the link to...'", "lineno": 654, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.611815Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\u2139\ufe0f No tool mentions detected in user message", "lineno": 708, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.612259Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "\ud83d\udd04 Running iteration 1 of 100...", "lineno": 729, "func_name": "run_agent", "filename": "run.py", "timestamp": "2025-07-04T02:43:19.614369Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Admin check for user fd8c1084-45ca-449a-9fc8-3b94ab53c239 (<EMAIL>): True", "lineno": 45, "func_name": "is_admin_user", "filename": "admin_utils.py", "timestamp": "2025-07-04T02:43:19.723146Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Admin user fd8c1084-45ca-449a-9fc8-3b94ab53c239 detected - bypassing billing checks", "lineno": 355, "func_name": "check_billing_status", "filename": "billing.py", "timestamp": "2025-07-04T02:43:19.723659Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Starting thread execution for thread 151a60bb-84eb-4a14-8afc-b40f4423b338", "lineno": 509, "func_name": "run_thread", "filename": "thread_manager.py", "timestamp": "2025-07-04T02:43:19.931119Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}
2025-07-03 22:43:19 worker-1    | {"level": "info", "event": "Using model: anthropic/claude-3-5-haiku-latest", "lineno": 510, "func_name": "run_thread", "filename": "thread_manager.py", "timestamp": "2025-07-04T02:43:19.931416Z", "thread_id": "151a60bb-84eb-4a14-8afc-b40f4423b338", "agent_run_id": "d031ec3a-030b-43a7-b756-8ff082aa5595", "request_id": "65ab46a5-bbb3-4e2e-bc19-82c683dcd3ac"}