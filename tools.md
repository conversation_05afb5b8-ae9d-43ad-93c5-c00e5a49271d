- gmail

GMAIL_DELETE_DRAFT
Permanently deletes a specific gmail draft using its id; ensure the draft exists and the user has necessary permissions for the given `user id`.
GMAIL_DELETE_MESSAGE
Permanently deletes a specific email message by its id from a gmail mailbox; for `user id`, use 'me' for the authenticated user or an email address to which the authenticated user has delegated access.
GMAIL_FETCH_EMAILS
Fetches a list of email messages from a gmail account, supporting filtering, pagination, and optional full content retrieval.
GMAIL_GET_CONTACTS
Fetches contacts (connections) for the authenticated google account, allowing selection of specific data fields and pagination.
GMAIL_LIST_DRAFTS
Retrieves a paginated list of email drafts from a user's gmail account.
GMAIL_MOVE_TO_TRASH
Moves an existing, non-deleted email message to the trash for the specified user.
GMAIL_PATCH_LABEL
Patches the specified label.
GMAIL_REPLY_TO_THREAD
Sends a reply within a specific gmail thread using the original thread's subject, requiring a valid `thread id` and correctly formatted email addresses.
GMAIL_SEARCH_PEOPLE
Searches contacts by matching the query against names, nicknames, emails, phone numbers, and organizations, optionally including 'other contacts'.
GMAIL_SEND_DRAFT
Sends the specified, existing draft to the recipients in the to, cc, and bcc headers.
GMAIL_SEND_EMAIL
Sends an email via gmail api using the authenticated user's google profile display name, requiring `is html=true` if the body contains html and valid `s3key`, `mimetype`, `name` for any attachment.
GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID
Fetches a specific email message by its id, provided the `message id` exists and is accessible to the authenticated `user id`.
GMAIL_CREATE_EMAIL_DRAFT
Creates a gmail email draft, supporting to/cc/bcc, subject, plain/html body (ensure `is html=true` for html), attachments, and threading.
GMAIL_FETCH_MESSAGE_BY_THREAD_ID
Retrieves messages from a gmail thread using its `thread id`, where the thread must be accessible by the specified `user id`.
GMAIL_LIST_THREADS
Retrieves a list of email threads from a gmail account, identified by `user id` (email address or 'me'), supporting filtering and pagination.

- slack

SLACK_ADD_REACTION_TO_AN_ITEM
Adds a specified emoji reaction to an existing message in a slack channel, identified by its timestamp; does not remove or retrieve reactions.
SLACK_CREATE_A_REMINDER
Creates a slack reminder with specified text and time; time accepts unix timestamps, seconds from now, or natural language (e.g., 'in 15 minutes', 'every thursday at 2pm').
SLACK_FETCH_CONVERSATION_HISTORY
Fetches a chronological list of messages and events from a specified slack conversation, accessible by the authenticated user/bot, with options for pagination and time range filtering.
SLACK_LIST_ALL_SLACK_TEAM_CHANNELS_WITH_VARIOUS_FILTERS
Retrieves public channels, private channels, multi-person direct messages (mpims), and direct messages (ims) from a slack workspace, with options to filter by type and exclude archived channels.
SLACK_LIST_ALL_SLACK_TEAM_USERS_WITH_PAGINATION
Retrieves a paginated list of all users, including comprehensive details, profile information, status, and team memberships, in a slack workspace; data may not be real-time.
SLACK_REMOVE_REACTION_FROM_ITEM
Removes an emoji reaction from a message, file, or file comment in slack.
SLACK_SCHEDULES_A_MESSAGE_TO_A_CHANNEL_AT_A_SPECIFIED_TIME
Schedules a message to a slack channel, dm, or private group for a future time (`post at`), requiring `text`, `blocks`, or `attachments` for content; scheduling is limited to 120 days in advance.
SLACK_SEARCH_FOR_MESSAGES_WITH_QUERY
Searches messages in a slack workspace using a query with optional modifiers (e.g., `in:`, `from:`, `has:`, `before:`) across accessible channels, dms, and private groups.
SLACK_SENDS_A_MESSAGE_TO_A_SLACK_CHANNEL
Posts a message to a slack channel, direct message, or private group; requires content via `text`, `blocks`, or `attachments`.
SLACK_UPDATES_A_SLACK_MESSAGE
Updates a slack message, identified by `channel` id and `ts` timestamp, by modifying its `text`, `attachments`, or `blocks`; provide at least one content field, noting `attachments`/`blocks` are replaced if included (`[]` clears them).
SLACK_CONVERSATIONS_HISTORY
Deprecated: use `fetch conversation history`. retrieves message history from a slack conversation accessible by the authenticated user/bot, with pagination and time filtering.
SLACK_CONVERSATIONS_INFO
Deprecated (use `retrieve conversation information`): retrieves metadata (name, purpose, creation date) for a slack conversation, excluding messages.
SLACK_CONVERSATIONS_LIST
(deprecated) retrieves public channels, private channels, mpims, and ims from a slack workspace; use `list all slack team channels with various filters` instead.
SLACK_USERS_INFO
Fetches detailed information (excluding message history or channel memberships) for a specific slack user; deprecated, use retrieve detailed user information.
SLACK_USERS_PROFILE_GET_PROFILE_INFO
Deprecated: use `retrieve user profile information`. retrieves profile information for a slack user (defaults to authenticated user if `user` id is omitted); a provided `user` id must be valid.
SLACK_LISTS_PINNED_ITEMS_IN_A_CHANNEL
Retrieves all messages and files pinned to a specified channel; the caller must have access to this channel.

- linear

LINEAR_REMOVE_ISSUE_LABEL
Removes a specified label from an existing linear issue using their ids; successful even if the label isn't on the issue.
LINEAR_UPDATE_ISSUE
Updates an existing linear issue using its `issue id`; requires at least one other attribute for modification, and all provided entity ids (for state, assignee, labels, etc.) must be valid.
LINEAR_CREATE_LINEAR_COMMENT
Creates a new comment on a specified linear issue.
LINEAR_CREATE_LINEAR_ISSUE
Creates a new issue in a specified linear project and team, requiring a title and description, and allowing for optional properties like assignee, state, priority, and due date.
LINEAR_CREATE_LINEAR_LABEL
Creates a new label in linear for a specified team, used to categorize and organize issues.
LINEAR_DELETE_LINEAR_ISSUE
Archives an existing linear issue by its id, which is linear's standard way of deleting issues; the operation is idempotent.
LINEAR_GET_LINEAR_ISSUE
Retrieves an existing linear issue's comprehensive details, including title, description, attachments, and comments.
LINEAR_LIST_LINEAR_CYCLES
Retrieves all cycles (time-boxed iterations for work) from the linear account; no filters are applied.
LINEAR_LIST_LINEAR_ISSUES
Lists non-archived linear issues; if project id is not specified, issues from all accessible projects are returned.
LINEAR_LIST_LINEAR_LABELS
Retrieves all labels associated with a given team id in linear; the team id must refer to an existing team.
LINEAR_LIST_LINEAR_PROJECTS
Retrieves all projects from the linear account.
LINEAR_LIST_LINEAR_STATES
Retrieves all workflow states for a specified team in linear, representing the stages an issue progresses through in that team's workflow.
LINEAR_LIST_LINEAR_TEAMS
Retrieves all teams, including their members, and filters each team's associated projects by the provided 'project id'.
LINEAR_RUN_QUERY_OR_MUTATION
Executes a user-provided graphql query or mutation against the linear api, intended for advanced data operations or those not covered by other specific actions.
LINEAR_CREATE_LINEAR_ATTACHMENT
Creates a new attachment and associates it with a specific, existing linear issue.

- google docs

GOOGLEDOCS_COPY_DOCUMENT
Tool to create a copy of an existing google document. use this to duplicate a document, for example, when using an existing document as a template. the copied document will have a default title (e.g., 'copy of [original title]') if no new title is provided, and will be placed in the user's root google drive folder.
GOOGLEDOCS_CREATE_FOOTER
Tool to create a new footer in a google document. use when you need to add a footer, optionally specifying its type and the section it applies to.
GOOGLEDOCS_CREATE_HEADER
Tool to create a new header in a google document. use this tool when you need to add a header to a document, optionally specifying the section it applies to.
GOOGLEDOCS_CREATE_NAMED_RANGE
Tool to create a new named range in a google document. use this to assign a name to a specific part of the document for easier reference or programmatic manipulation.
GOOGLEDOCS_CREATE_PARAGRAPH_BULLETS
Tool to add bullets to paragraphs within a specified range in a google document. use when you need to format a list or a set of paragraphs as bullet points.
GOOGLEDOCS_DELETE_CONTENT_RANGE
Tool to delete a range of content from a google document. use when you need to remove a specific portion of text or other structural elements within a document.
GOOGLEDOCS_DELETE_FOOTER
Tool to delete a footer from a google document. use when you need to remove a footer from a specific section or the default footer.
GOOGLEDOCS_DELETE_HEADER
Deletes the header from the specified section or the default header if no section is specified. use this tool to remove a header from a google document.
GOOGLEDOCS_DELETE_NAMED_RANGE
Tool to delete a named range from a google document. use when you need to remove a previously defined named range by its id or name.
GOOGLEDOCS_DELETE_PARAGRAPH_BULLETS
Tool to remove bullets from paragraphs within a specified range in a google document. use when you need to clear bullet formatting from a section of a document.
GOOGLEDOCS_DELETE_TABLE
Tool to delete an entire table from a google document. use when you have the document id and the specific start and end index of the table element to be removed. the table's range can be found by inspecting the document's content structure.
GOOGLEDOCS_DELETE_TABLE_COLUMN
Tool to delete a column from a table in a google document. use this tool when you need to remove a specific column from an existing table within a document.
GOOGLEDOCS_DELETE_TABLE_ROW
Tool to delete a row from a table in a google document. use when you need to remove a specific row from an existing table.
GOOGLEDOCS_GET_CHARTS_FROM_SPREADSHEET
Tool to retrieve a list of all charts from a specified google sheets spreadsheet. use when you need to get chart ids and their specifications for embedding or referencing elsewhere, such as in google docs.
GOOGLEDOCS_INSERT_PAGE_BREAK
Tool to insert a page break into a google document. use when you need to start new content on a fresh page, such as at the end of a chapter or section.
GOOGLEDOCS_INSERT_TABLE_ACTION
Tool to insert a table into a google document. use when you need to add a new table at a specific location or at the end of a segment (like document body, header, or footer) in a document.
GOOGLEDOCS_INSERT_TEXT_ACTION
Tool to insert a string of text at a specified location within a google document. use when you need to add new text content to an existing document.
GOOGLEDOCS_REPLACE_ALL_TEXT
Tool to replace all occurrences of a specified text string with another text string throughout a google document. use when you need to perform a global find and replace operation within a document.
GOOGLEDOCS_UPDATE_DOCUMENT_STYLE
Tool to update the overall document style, such as page size, margins, and default text direction. use when you need to modify the global style settings of a google document.
GOOGLEDOCS_CREATE_DOCUMENT
Creates a new google docs document using the provided title as filename and inserts the initial text at the beginning if non-empty, returning the document's id and metadata (excluding body content).
GOOGLEDOCS_GET_DOCUMENT_BY_ID
Retrieves an existing google document by its id; will error if the document is not found.
GOOGLEDOCS_SEARCH_DOCUMENTS
Search for google documents using various filters including name, content, date ranges, and more.

- google calendar

GOOGLECALENDAR_PATCH_EVENT
Updates specified fields of an existing event in a google calendar using patch semantics (array fields like `attendees` are fully replaced if provided); ensure the `calendar id` and `event id` are valid and the user has write access to the calendar.
GOOGLECALENDAR_CALENDARS_UPDATE
Updates metadata for a calendar.
GOOGLECALENDAR_CLEAR_CALENDAR
Clears a primary calendar. this operation deletes all events associated with the primary calendar of an account.
GOOGLECALENDAR_CREATE_EVENT
Creates an event on a google calendar, needing rfc3339 utc start/end times (end after start) and write access to the calendar.
GOOGLECALENDAR_DELETE_EVENT
Deletes a specified event by `event id` from a google calendar (`calendar id`); this action is idempotent and raises a 404 error if the event is not found.
GOOGLECALENDAR_DUPLICATE_CALENDAR
Creates a new, empty google calendar with the specified title (summary).
GOOGLECALENDAR_EVENTS_INSTANCES
Returns instances of the specified recurring event.
GOOGLECALENDAR_EVENTS_LIST
Returns events on the specified calendar.
GOOGLECALENDAR_EVENTS_MOVE
Moves an event to another calendar, i.e., changes an event's organizer.
GOOGLECALENDAR_FIND_EVENT
Finds events in a specified google calendar using text query, time ranges (event start/end, last modification), and event types; ensure `timemin` is not chronologically after `timemax` if both are provided.
GOOGLECALENDAR_GET_CALENDAR
Retrieves a specific google calendar, identified by `calendar id`, to which the authenticated user has access.
GOOGLECALENDAR_GET_CURRENT_DATE_TIME
Gets the current date and time, allowing for a specific timezone offset.
GOOGLECALENDAR_LIST_CALENDARS
Retrieves calendars from the user's google calendar list, with options for pagination and filtering.
GOOGLECALENDAR_QUICK_ADD
Parses natural language text to quickly create a basic google calendar event with its title, date, and time, suitable for simple scheduling; does not support recurring events or direct attendee addition via parameters, and `calendar id` must be valid if not 'primary'.
GOOGLECALENDAR_REMOVE_ATTENDEE
Removes an attendee from a specified event in a google calendar; the calendar and event must exist.
GOOGLECALENDAR_UPDATE_EVENT
Updates an existing event by `event id` in a google calendar; this is a full put replacement, so provide all desired fields as unspecified ones may be cleared or reset.
GOOGLECALENDAR_FIND_FREE_SLOTS
Finds free/busy time slots in google calendars for specified calendars within a defined time range (defaults to the current day utc if `time min`/`time max` are omitted), enhancing busy intervals with event details; `time min` must precede `time max` if both are provided.

- google sheets

GOOGLESHEETS_ADD_SHEET
Adds a new sheet (worksheet) to a spreadsheet. use this tool to create a new tab within an existing google sheet, optionally specifying its title, index, size, and other properties.
GOOGLESHEETS_CLEAR_BASIC_FILTER
Tool to clear the basic filter from a sheet. use when you need to remove an existing basic filter from a specific sheet within a google spreadsheet.
GOOGLESHEETS_DELETE_SHEET
Tool to delete a sheet (worksheet) from a spreadsheet. use when you need to remove a specific sheet from a google sheet document.
GOOGLESHEETS_GET_SPREADSHEET_BY_DATA_FILTER
Returns the spreadsheet at the given id, filtered by the specified data filters. use this tool when you need to retrieve specific subsets of data from a google sheet based on criteria like a1 notation, developer metadata, or grid ranges.
GOOGLESHEETS_GET_SPREADSHEET_INFO
Retrieves comprehensive metadata for a google spreadsheet using its id, excluding cell data.
GOOGLESHEETS_INSERT_DIMENSION
Tool to insert new rows or columns into a sheet at a specified location. use when you need to add empty rows or columns within an existing google sheet.
GOOGLESHEETS_SET_BASIC_FILTER
Tool to set a basic filter on a sheet in a google spreadsheet. use when you need to filter or sort data within a specific range on a sheet.
GOOGLESHEETS_SPREADSHEETS_VALUES_APPEND
Tool to append values to a spreadsheet. use when you need to add new data to the end of an existing table in a google sheet.
GOOGLESHEETS_SPREADSHEETS_VALUES_BATCH_CLEAR
Tool to clear one or more ranges of values from a spreadsheet. use when you need to remove data from specific cells or ranges while keeping formatting and other properties intact.
GOOGLESHEETS_SPREADSHEETS_VALUES_BATCH_GET_BY_DATA_FILTER
Tool to return one or more ranges of values from a spreadsheet that match the specified data filters. use when you need to retrieve specific data sets based on filtering criteria rather than entire sheets or fixed ranges.
GOOGLESHEETS_UPDATE_SPREADSHEET_PROPERTIES
Tool to update properties of a spreadsheet, such as its title, locale, or auto-recalculation settings. use when you need to modify the overall configuration of a google sheet.
GOOGLESHEETS_BATCH_GET
Retrieves data from specified cell ranges in a google spreadsheet; ensure the spreadsheet has at least one worksheet and any explicitly referenced sheet names in ranges exist.
GOOGLESHEETS_BATCH_UPDATE
Updates a specified range in a google sheet with given values, or appends them as new rows if `first cell location` is omitted; ensure the target sheet exists and the spreadsheet contains at least one worksheet.
GOOGLESHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER
Tool to update values in ranges matching data filters. use when you need to update specific data in a google sheet based on criteria rather than fixed cell ranges.
GOOGLESHEETS_CLEAR_VALUES
Clears cell content (preserving formatting and notes) from a specified a1 notation range in a google spreadsheet; the range must correspond to an existing sheet and cells.
GOOGLESHEETS_CREATE_GOOGLE_SHEET1
Creates a new google spreadsheet in google drive using the provided title.
GOOGLESHEETS_FIND_WORKSHEET_BY_TITLE
Finds a worksheet by its exact, case-sensitive title within a google spreadsheet; returns a boolean indicating if found and the complete metadata of the entire spreadsheet, regardless of whether the target worksheet is found.
GOOGLESHEETS_FORMAT_CELL
Applies text and background cell formatting to a specified range in a google sheets worksheet.
GOOGLESHEETS_SEARCH_SPREADSHEETS
Search for google spreadsheets using various filters including name, content, date ranges, and more.

- twitter

TWITTER_CREATE_LIST
Creates a new, empty list on x (formerly twitter), for which the provided name must be unique for the authenticated user; accounts are added separately.
TWITTER_CREATION_OF_A_POST
Creates a tweet on twitter; `text` is required unless `card uri`, `media media ids`, `poll options`, or `quote tweet id` is provided.
TWITTER_DELETE_LIST
Permanently deletes a specified twitter list using its id, which must be owned by the authenticated user; this action is irreversible and the list must already exist.
TWITTER_FOLLOW_USER
Allows an authenticated user (path `id`) to follow another user (`target user id`), which results in a pending request if the target user's tweets are protected.
TWITTER_GET_BLOCKED_USERS
Retrieves user objects for accounts blocked by the specified user id; this is a read-only view of a user's block list.
TWITTER_POST_DELETE_BY_POST_ID
Irreversibly deletes a specific tweet by its id; the tweet may persist in third-party caches after deletion.
TWITTER_UNFOLLOW_USER
Allows the authenticated `source user id` to unfollow an existing twitter user (`target user id`), which removes the follow relationship.
TWITTER_USER_LOOKUP_BY_USERNAME
Fetches public profile information for a valid and existing twitter user by their username, optionally expanding related data like pinned tweets; results may be limited for protected profiles not followed by the authenticated user.
TWITTER_USER_LOOKUP_ME
Returns profile information for the currently authenticated x user, customizable via request fields.
TWITTER_CREATE_A_NEW_DM_CONVERSATION
Creates a new group direct message (dm) conversation on twitter with specified participant ids and an initial message, which can include text and media attachments.
TWITTER_RETWEET_POST
Retweets a tweet (`tweet id`) for a given user (`id`), provided the tweet is public (or user follows if protected), not already retweeted by the user, and its author has not blocked the user.
TWITTER_USER_LIKE_POST
Allows the authenticated user (`id`) to like a specific, accessible tweet (`tweet id`), provided neither user has blocked the other and the authenticated user is not restricted from liking.
TWITTER_FOLLOW_A_LIST
Allows the authenticated user (`id`) to follow a specific twitter list (`list id`) they are permitted to access, subscribing them to the list's timeline; this does not automatically follow individual list members.

- airtable

AIRTABLE_LIST_BASES
Retrieves all airtable bases accessible to the authenticated user, which may include an 'offset' for pagination.
AIRTABLE_CREATE_BASE
Creates a new airtable base with specified tables and fields within a workspace; ensure field options are valid for their type.
AIRTABLE_CREATE_COMMENT
Creates a new comment on a specific record within an airtable base and table.
AIRTABLE_CREATE_FIELD
Creates a new field within a specified table in an airtable base.
AIRTABLE_CREATE_MULTIPLE_RECORDS
Creates multiple new records in a specified airtable table.
AIRTABLE_CREATE_RECORD
Creates a new record in a specified airtable table; field values must conform to the table's column types.
AIRTABLE_CREATE_TABLE
Creates a new table within a specified existing airtable base, allowing definition of its name, description, and field structure.
AIRTABLE_DELETE_COMMENT
Deletes an existing comment from a specified record in an airtable table.
AIRTABLE_DELETE_MULTIPLE_RECORDS
Deletes up to 10 specified records from a table within an airtable base.
AIRTABLE_DELETE_RECORD
Permanently deletes a specific record from an existing table within an existing airtable base.
AIRTABLE_GET_BASE_SCHEMA
Retrieves the detailed schema for a specified airtable base, including its tables, fields, field types, and configurations, using the `baseid`.
AIRTABLE_GET_RECORD
Retrieves a specific record from a table within an airtable base.
AIRTABLE_GET_USER_INFO
Retrieves information, such as id and permission scopes, for the currently authenticated airtable user from the `/meta/whoami` endpoint.
AIRTABLE_LIST_COMMENTS
Retrieves all comments for a specific record in an airtable table, requiring existing `baseid`, `tableidorname`, and `recordid`.
AIRTABLE_LIST_RECORDS
Retrieves records from an airtable table, with options for filtering, sorting, pagination, and specifying returned fields.
AIRTABLE_UPDATE_MULTIPLE_RECORDS
Updates multiple existing records in a specified airtable table; these updates are not performed atomically.
AIRTABLE_UPDATE_RECORD
Modifies specified fields of an existing record in an airtable base and table; the base, table, and record must exist.

- redidt

REDDIT_EDIT_REDDIT_COMMENT_OR_POST
Edits the body text of the authenticated user's own existing comment or self-post on reddit; cannot edit link posts or titles.
REDDIT_CREATE_REDDIT_POST
Creates a new text or link post on a specified, existing reddit subreddit, optionally applying a flair.
REDDIT_DELETE_REDDIT_COMMENT
Deletes a reddit comment, identified by its fullname id, if it was authored by the authenticated user.
REDDIT_DELETE_REDDIT_POST
Permanently deletes a reddit post by its id, provided the authenticated user has deletion permissions for that post.
REDDIT_GET_USER_FLAIR
Fetches the list of available link flairs (i.e., post flairs, not user flairs) for a given subreddit.
REDDIT_POST_REDDIT_COMMENT
Posts a comment on reddit, replying to an existing and accessible submission (post) or another comment.
REDDIT_RETRIEVE_POST_COMMENTS
Retrieves all comments for a reddit post given its article id (which must be for an existing, public post); nested replies within comments are returned as raw dictionaries requiring parsing.
REDDIT_RETRIEVE_REDDIT_POST
Retrieves the current hot posts from a specified, publicly accessible subreddit.
REDDIT_RETRIEVE_SPECIFIC_COMMENT
Retrieves detailed information for a specific reddit comment or post using its fullname.
REDDIT_SEARCH_ACROSS_SUBREDDITS
Searches reddit for content (e.g., posts, comments) using a query, with results typically confined to subreddits unless `restrict sr` is set to false.

- zoom

ZOOM_CREATE_A_MEETING
Enable zoom meeting creation via user-level apps with "me". "start url" for hosts expires in 2 hours, or 90 days for "custcreate" users. renew via api, capped at 100 requests/day. requires "meeting:write" permission, subject to medium rate limit.
ZOOM_GET_A_MEETING
The text provides details on api permissions for reading meeting information, categorizing permissions into general and granular scopes, and labels the rate limit as 'light'.
ZOOM_GET_A_MEETING_SUMMARY
Meeting summary info requires a pro+ host plan, ai companion enabled, excluding e2ee meetings. scopes include meeting summary:read and admin versions. rate limit: light.
ZOOM_GET_MEETING_RECORDINGS
To download meeting recordings, use `download url`. include oauth token in the header for passcode-protected ones. supports `recording:read` and `phone recording:read:admin` scopes, with a `light` rate limit.
ZOOM_LIST_ALL_RECORDINGS
This text details how to list zoom cloud recordings for a user, notably by using "me" for user-level apps and requiring an oauth token for access. it requires a pro plan, cloud recording enabled, and has a medium rate limit.
ZOOM_LIST_MEETINGS
This zoom api lists a user's scheduled meetings using the `me` value for user-level apps, excluding instant meetings and only showing unexpired ones. it requires specific scopes and has a `medium` rate limit.
ZOOM_UPDATE_MEETING_STATUS
Update the status of a meeting. **scopes:** `meeting:write:admin`,`meeting:write` [\*\*rate limit label](https://marketplace.zoom.us/docs/api-reference/rate-limits#rate-limits):** `light`
ZOOM_ADD_A_MEETING_REGISTRANT
This text guides on creating and customizing a user's registration for a zoom meeting, with a max of 4,999 registrants. preconditions include the host being licensed. api scopes and a light rate limit apply.
ZOOM_GET_MEETING_INVITATION
Retrieve the meeting invitation note for a specific meeting. **scopes:** `meeting:read`,`meeting:read:admin` [**rate limit label](https://marketplace.zoom.us/docs/api-reference/rate-limits#rate-limits):\*\* `light`
ZOOM_GET_MEETING_RECORDING_S_ANALYTICS_SUMMARY
Access zoom meeting recording analytics with a 1-month max duration using an oauth token. use specific api scopes and note the medium rate limit. example: `curl` command with authorization header.

- SalesForce

SALESFORCE_ACCOUNT_CREATION_WITH_CONTENT_TYPE_OPTION
Creates a new salesforce account using a json post request, requiring 'name'; specific fields (e.g., custom, dunsnumber) may have org-level prerequisites.
SALESFORCE_CREATE_CAMPAIGN_RECORD_VIA_POST
Creates a new campaign record in salesforce; if 'parentid' is provided, it must be a valid id of an existing campaign record, and if 'ownerid' is provided, it must be a valid id of an active user.
SALESFORCE_CREATE_LEAD_WITH_SPECIFIED_CONTENT_TYPE
Creates a new lead in salesforce, requiring `lastname` and `company` unless person accounts are enabled and `company` is null.
SALESFORCE_CREATE_NOTE_RECORD_WITH_CONTENT_TYPE_HEADER
Creates a new note record in salesforce, associated with an existing salesforce object via `parentid`, automatically including a `content-type: application/json` header.
SALESFORCE_EXECUTE_SOQL_QUERY
Executes the provided soql query against salesforce; the query must begin with 'select'.
SALESFORCE_FETCH_MODIFIED_OR_UNMODIFIED_SOBJECTS
Fetches sobjects from `/sobjects` based on caller-set 'if-modified-since' (returns objects modified after a date, or 304 status if none) or 'if-unmodified-since' (returns objects if unmodified since a date, or 412 status if modified) http headers.
SALESFORCE_QUERY_CONTACTS_BY_NAME
Finds salesforce contact records by name using a case-insensitive search.
SALESFORCE_REMOVE_ACCOUNT_BY_UNIQUE_IDENTIFIER
Deletes an existing salesforce account using its unique id, returning an empty response on success (http 204).
SALESFORCE_RETRIEVE_ACCOUNT_DATA_AND_ERROR_RESPONSES
Retrieves comprehensive metadata for the salesforce account sobject, detailing its properties, recent records, and related resource urls.
SALESFORCE_RETRIEVE_CAMPAIGN_DATA_WITH_ERROR_HANDLING
Retrieves comprehensive information and metadata for the salesforce campaign sobject, provided it is enabled and accessible in the organization, and features robust error handling.
SALESFORCE_RETRIEVE_LEAD_BY_ID
Retrieves details for a salesforce lead by its id; the specified lead id must exist in salesforce.
SALESFORCE_RETRIEVE_LEAD_DATA_WITH_VARIOUS_RESPONSES
Retrieves lead sobject data from salesforce, such as recently viewed leads or general lead object information.
SALESFORCE_RETRIEVE_NOTE_WITH_CONDITIONS
Retrieves a salesforce note object by its id, optionally specifying which fields to return; the note id must exist.
SALESFORCE_RETRIEVE_OPPORTUNITIES_DATA
Retrieves all available opportunity records, representing potential revenue-generating deals, from salesforce.
SALESFORCE_RETRIEVE_SPECIFIC_CONTACT_BY_ID
Retrieves a salesforce contact by its unique id; the id must correspond to an existing contact record in salesforce.
SALESFORCE_UPDATE_CONTACT_BY_ID
Updates specified fields of an existing salesforce contact by its id; at least one field must be provided for modification.

- Google drive

GOOGLEDRIVE_CREATE_COMMENT
Tool to create a comment on a file. use when you need to add a new comment to a specific file in google drive.
GOOGLEDRIVE_CREATE_DRIVE
Tool to create a new shared drive. use when you need to programmatically create a new shared drive for collaboration or storage.
GOOGLEDRIVE_CREATE_FILE
Creates a new file or folder with metadata. use to create empty files or folders, or files with content by providing it in the request body (though this action primarily focuses on metadata creation).
GOOGLEDRIVE_CREATE_FOLDER
Creates a new folder in google drive, optionally within a parent folder specified by its id or name; if a parent name is provided but not found, the action will fail.
GOOGLEDRIVE_DOWNLOAD_FILE
Downloads a file from google drive by its id, optionally exporting google workspace documents (docs, sheets, slides) to a specified `mime type`; for other file types, `mime type` must be omitted.
GOOGLEDRIVE_GET_PERMISSION
Gets a permission by id. use this tool to retrieve a specific permission for a file or shared drive.
GOOGLEDRIVE_GET_REPLY
Tool to get a specific reply to a comment on a file. use when you need to retrieve the details of a particular reply.
GOOGLEDRIVE_LIST_CHANGES
Tool to list the changes for a user or shared drive. use when you need to track modifications to files and folders, such as creations, deletions, or permission changes. this action requires a `pagetoken` which can be initially obtained using the `get changes start page token` action or from a previous `list changes` response.
GOOGLEDRIVE_LIST_FILES
Tool to list a user's files and folders in google drive. use this to search or browse for files and folders based on various criteria.
GOOGLEDRIVE_LIST_SHARED_DRIVES
Tool to list the user's shared drives. use when you need to get a list of all shared drives accessible to the authenticated user.
GOOGLEDRIVE_MOVE_FILE
Tool to move a file from one folder to another in google drive. use when you need to reorganize files by changing their parent folder(s).
GOOGLEDRIVE_PARSE_FILE
Deprecated: exports google workspace files (max 10mb) to a specified format using `mime type`, or downloads other file types; use `googledrive download file` instead.
GOOGLEDRIVE_UPLOAD_FILE
Uploads a file (max 5mb) to google drive, moving it to a specified folder if a valid folder id is provided, otherwise uploads to root.
GOOGLEDRIVE_GENERATE_IDS
Generates a set of file ids which can be provided in create or copy requests. use when you need to pre-allocate ids for new files or copies.
GOOGLEDRIVE_COPY_FILE
Duplicates an existing file in google drive, identified by its `file id`.
GOOGLEDRIVE_CREATE_FILE_FROM_TEXT
Creates a new file in google drive from provided text content (up to 10mb), supporting various formats including automatic conversion to google workspace types.
GOOGLEDRIVE_EDIT_FILE
Updates an existing google drive file by overwriting its entire content with new text (max 10mb).
GOOGLEDRIVE_FIND_FILE
Tool to list or search for files and folders in google drive. use when you need to find specific files based on query criteria or list contents of a drive/folder.
GOOGLEDRIVE_FIND_FOLDER
Tool to find a folder in google drive by its name and optionally a parent folder. use when you need to locate a specific folder to perform further actions like creating files in it or listing its contents.
GOOGLEDRIVE_GOOGLE_DRIVE_DELETE_FOLDER_OR_FILE_ACTION
Tool to delete a file or folder in google drive. use when you need to permanently remove a specific file or folder using its id. note: this action is irreversible.

- Notion

NOTION_ADD_PAGE_CONTENT
Appends a single content block to a notion page or a parent block (must be page, toggle, to-do, bulleted/numbered list, callout, or quote); invoke repeatedly to add multiple blocks.
NOTION_CREATE_DATABASE
Creates a new notion database as a subpage under a specified parent page with a defined properties schema; use this action exclusively for creating new databases.
NOTION_CREATE_NOTION_PAGE
Creates a new page in a notion workspace.
NOTION_DELETE_BLOCK
Archives a notion block, page, or database using its id, which sets its 'archived' property to true (like moving to "trash" in the ui) and allows it to be restored later.
NOTION_FETCH_DATA
Simplifies the retrieval of notion items by abstracting the native notion api's complexity.
NOTION_FETCH_DATABASE
Fetches a notion database's structural metadata (properties, title, etc.) via its `database id`, not the data entries; `database id` must reference an existing database.
NOTION_FETCH_ROW
Retrieves a notion database row's properties and metadata; use a different action for page content blocks.
NOTION_GET_ABOUT_USER
Retrieves detailed information about a specific notion user, such as their name, avatar, and email, based on their unique user id.
NOTION_INSERT_ROW_DATABASE
Creates a new page (row) in a specified notion database.
NOTION_LIST_USERS
Retrieves a paginated list of users (excluding guests) from the notion workspace; the number of users returned per page may be less than the requested `page size`.
NOTION_QUERY_DATABASE
Queries a notion database for pages (rows), where rows are pages and columns are properties; ensure sort property names correspond to existing database properties.
NOTION_RETRIEVE_COMMENT
Tool to retrieve a specific comment by its id. use when you have a comment id and need to fetch its details.
NOTION_RETRIEVE_DATABASE_PROPERTY
Tool to retrieve a specific property object of a notion database. use when you need to get details about a single database column/property.
NOTION_UPDATE_PAGE
Tool to update the properties, icon, cover, or archive status of a page. use when you need to modify existing page attributes.
NOTION_UPDATE_SCHEMA_DATABASE
Updates an existing notion database's title, description, and/or properties; at least one of these attributes must be provided to effect a change.
NOTION_APPEND_BLOCK_CHILDREN
Appends new child blocks to a specified parent block or page in notion, ideal for adding content within an existing structure (e.g., list items, toggle content) rather than creating new pages; the parent must be able to accept children.
NOTION_FETCH_NOTION_BLOCK
Retrieves a notion block (or page, as pages are blocks) using its valid uuid; if the block has children, use a separate action to fetch them.
NOTION_FETCH_NOTION_CHILD_BLOCK
Retrieves a paginated list of direct, first-level child block objects for a given parent notion block or page id; use block ids from the response for subsequent calls to access deeply nested content.
NOTION_GET_PAGE_PROPERTY_ACTION
Call this to get a specific property from a notion page when you have a valid `page id` and `property id`; handles pagination for properties returning multiple items.
NOTION_NOTION_UPDATE_BLOCK
Updates an existing notion block's textual content or type-specific properties (e.g., 'checked' status, 'color'), using its `block id` and the specified `block type`.
NOTION_SEARCH_NOTION_PAGE
Searches notion pages and databases by title; an empty query lists all accessible items, useful for discovering ids or as a fallback when a specific query yields no results.

- Hubspot

HUBSPOT_CREATE_PRODUCT_BATCH
Creates multiple products in hubspot crm in a batch, allowing unique properties and associations per product; ensure `associationcategory` and `associationtypeid` are valid, and `to id` refers to an existing crm object id when specifying associations.
HUBSPOT_CREATE_PRODUCT_OBJECT
Creates a new product in hubspot with specified properties and optional associations; ensure `hs sku` is unique if provided, custom properties are pre-defined in hubspot, and any association `to id` and `associationtypeid` are valid.
HUBSPOT_LIST_PRODUCTS_WITH_PAGING
Retrieves a paginated list of products from hubspot crm, optionally including specific properties, their history, associated object ids, and filtering by archived status.
HUBSPOT_UPDATE_PRODUCT
Partially updates an existing hubspot crm product, identified by `productid` (or a custom unique `idproperty`), by modifying only specified `properties`.
HUBSPOT_CAMPAIGN_SEARCH
Searches for and retrieves a paginated list of hubspot marketing campaigns.
HUBSPOT_CREATE_A_BATCH_OF_COMPANIES
Creates multiple company records in hubspot crm in a single batch operation, processing up to 100 companies per request.
HUBSPOT_CREATE_A_BATCH_OF_CONTACTS
Deprecated: use the `create batch of contacts` action instead. creates multiple hubspot contact records.
HUBSPOT_CREATE_A_BATCH_OF_EMAILS
Creates multiple email objects for record-keeping (does not send emails) within hubspot crm in a single batch, allowing properties and associations to other crm objects to be set for each email.
HUBSPOT_CREATE_BATCH_OF_DEALS
Creates multiple deals in hubspot crm; ensure any associated object ids, deal stages, and pipeline ids specified are valid and exist within the hubspot account.
HUBSPOT_CREATE_WORKFLOW
Creates a new hubspot workflow to automate processes; ensure `enrollmentcriteria` and `actions` use properties relevant to the specified `objecttypeid`.
HUBSPOT_CUSTOMIZABLE_CONTACTS_PAGE_RETRIEVAL
Deprecated: use 'list contacts page' to retrieve a paginated list of hubspot contacts.
HUBSPOT_FETCH_CONTACT_DETAILS_BY_ID
Deprecated: use `read crm contact by id` instead. retrieves a hubspot crm contact by its unique internal hubspot id, allowing selection of properties, historical values, associated objects, and filtering for archived contacts.
HUBSPOT_FETCH_CONTACT_IDS
Fetches hubspot contact ids for a specified `campaignguid` (must be a valid uuid of an existing campaign) and `contacttype`, supporting date filtering and pagination.
HUBSPOT_GET_ALL_WORKFLOWS
Retrieves a list of workflow summaries (id, name, type, status) from hubspot, using the 'limit' parameter for pagination.
HUBSPOT_GET_CAMPAIGN_METRICS
Retrieves key attribution metrics for an existing marketing campaign, identified by its `campaignguid`, within an optional date range.
HUBSPOT_LIST
Retrieves a paginated list of hubspot emails, allowing selection of specific properties (with or without history), associated object ids, and filtering by archive status.
HUBSPOT_PARTIALLY_UPDATE_CONTACT_USING_CONTACT_ID
Partially updates specified properties for an existing hubspot contact, identified by its valid `contactid`.
HUBSPOT_UPDATE
Deprecated: partially updates an existing hubspot crm product, identified by `productid` (or a custom unique `idproperty`), by modifying specified `properties`; use 'update product' instead.
HUBSPOT_UPDATE_A_BATCH_OF_CAMPAIGNS
Updates properties for up to 50 existing hubspot marketing campaigns in a single batch operation.
HUBSPOT_UPDATE_A_BATCH_OF_CONTACTS
Updates specified properties for a batch of up to 100 hubspot contacts, identified by their vids; ensure property values use hubspot internal names and formats (e.g., for enums like 'lifecyclestage').
HUBSPOT_UPDATE_A_BATCH_OF_EMAILS
Updates multiple hubspot email engagement records in a batch, identifying each by its hubspot object `id` (or an alternative unique property via `idproperty`) and setting new string values for specified properties (which must be valid internal email property names).

- Teams

MICROSOFT_TEAMS_CREATE_MEETING
Use to schedule a new standalone microsoft teams online meeting, i.e., one not linked to any calendar event.
MICROSOFT_TEAMS_TEAMS_CREATE_CHAT
Creates a new chat; if a 'oneonone' chat with the specified members already exists, its details are returned, while 'group' chats are always newly created.
MICROSOFT_TEAMS_CHATS_GET_ALL_CHATS
Retrieves all microsoft teams chats a specified user is part of, supporting filtering, property selection, and pagination.
MICROSOFT_TEAMS_CHATS_GET_ALL_MESSAGES
Retrieves all messages from a specified microsoft teams chat using the microsoft graph api, automatically handling pagination; ensure `chat id` is valid and odata expressions in `filter` or `select` are correct.
MICROSOFT_TEAMS_TEAMS_CREATE_CHANNEL
Creates a new 'standard', 'private', or 'shared' channel within a specified microsoft teams team.
MICROSOFT_TEAMS_TEAMS_GET_MESSAGE
Retrieves a specific message from a microsoft teams channel using its team, channel, and message ids.
MICROSOFT_TEAMS_TEAMS_LIST
Retrieves microsoft teams accessible by the authenticated user, allowing filtering, property selection, and pagination.
MICROSOFT_TEAMS_TEAMS_LIST_CHANNELS
Retrieves channels for a specified microsoft teams team id (must be valid and for an existing team), with options to include shared channels, filter results, and select properties.
MICROSOFT_TEAMS_TEAMS_LIST_CHAT_MESSAGES
Retrieves messages (newest first) from an existing and accessible microsoft teams one-on-one chat, group chat, or channel thread, specified by `chat id`.
MICROSOFT_TEAMS_TEAMS_LIST_PEOPLE
Retrieves a list of people relevant to a specified user from microsoft graph, noting the `search` parameter is only effective if `user id` is 'me'.
MICROSOFT_TEAMS_TEAMS_POST_CHANNEL_MESSAGE
Posts a new text or html message to a specified channel in a microsoft teams team.
MICROSOFT_TEAMS_TEAMS_POST_CHAT_MESSAGE
Sends a non-empty message (text or html) to a specified, existing microsoft teams chat; content must be valid html if `content type` is 'html'.
MICROSOFT_TEAMS_TEAMS_POST_MESSAGE_REPLY
Sends a reply to an existing message, identified by `message id`, within a specific `channel id` of a given `team id` in microsoft teams.

- Outlook

OUTLOOK_DOWNLOAD_OUTLOOK_ATTACHMENT
Downloads a specific file attachment from an email message in a microsoft outlook mailbox; the attachment must contain 'contentbytes' (binary data) and not be a link or embedded item.
OUTLOOK_OUTLOOK_CALENDAR_CREATE_EVENT
Creates a new outlook calendar event, ensuring `start datetime` is chronologically before `end datetime`.
OUTLOOK_OUTLOOK_CREATE_CONTACT
Creates a new contact in a microsoft outlook user's contacts folder.
OUTLOOK_OUTLOOK_CREATE_DRAFT
Creates an outlook email draft with subject, body, recipients, and an optional attachment; attachments require a name, mimetype, and content.
OUTLOOK_OUTLOOK_GET_CONTACT
Retrieves a specific outlook contact by its `contact id` from the contacts of a specified `user id` (defaults to 'me' for the authenticated user).
OUTLOOK_OUTLOOK_GET_EVENT
Retrieves the full details of a specific calendar event by its id from a user's outlook calendar, provided the event exists.
OUTLOOK_OUTLOOK_GET_PROFILE
Retrieves the microsoft outlook profile for a specified user.
OUTLOOK_OUTLOOK_LIST_EVENTS
Retrieves events from a user's outlook calendar via microsoft graph api, supporting pagination, filtering, property selection, sorting, and timezone specification.
OUTLOOK_OUTLOOK_LIST_MESSAGES
Retrieves a list of email messages from a specified mail folder in an outlook mailbox, with options for filtering, pagination, and sorting; ensure 'user id' and 'folder' are valid, and all date/time strings are in iso 8601 format.
OUTLOOK_OUTLOOK_REPLY_EMAIL
Sends a plain text reply to an outlook email message, identified by `message id`, allowing optional cc and bcc recipients.
OUTLOOK_OUTLOOK_SEND_EMAIL
Sends an email with subject, body, recipients, and an optional attachment via microsoft graph api; attachments require a non-empty file with valid name and mimetype.
OUTLOOK_OUTLOOK_UPDATE_EMAIL
Updates specified properties of an existing email message; `message id` must identify a valid message within the specified `user id`'s mailbox.
OUTLOOK_LIST_OUTLOOK_ATTACHMENTS
Lists metadata (like name, size, and type, but not `contentbytes`) for all attachments of a specified outlook email message.
OUTLOOK_OUTLOOK_CREATE_DRAFT_REPLY
Creates a draft reply in the specified user's outlook mailbox to an existing message (identified by a valid `message id`), optionally including a `comment` and cc/bcc recipients.
OUTLOOK_OUTLOOK_DELETE_CONTACT
Permanently deletes an existing contact, using its `contact id` (obtainable via 'list user contacts' or 'get contact'), from the outlook contacts of the user specified by `user id`.
OUTLOOK_OUTLOOK_DELETE_EVENT
Deletes an existing calendar event, identified by its unique `event id`, from a specified user's microsoft outlook calendar, with an option to send cancellation notifications to attendees.
OUTLOOK_OUTLOOK_GET_MESSAGE
Retrieves a specific email message by its id from the specified user's outlook mailbox, provided the message id exists for that user.
OUTLOOK_OUTLOOK_GET_SCHEDULE
Retrieves free/busy schedule information for specified email addresses within a defined time window.
OUTLOOK_OUTLOOK_LIST_CONTACTS
Retrieves a user's microsoft outlook contacts, from the default or a specified contact folder.
OUTLOOK_OUTLOOK_SEARCH_MESSAGES
Searches messages in a microsoft 365 or enterprise outlook account mailbox, supporting filters for sender, subject, attachments, pagination, and sorting by relevance or date.
OUTLOOK_OUTLOOK_UPDATE_CALENDAR_EVENT
Updates specified fields of an existing outlook calendar event.
OUTLOOK_OUTLOOK_UPDATE_CONTACT
Updates an existing outlook contact, identified by `contact id` for the specified `user id`, requiring at least one other field to be modified.

- ClickUp

CLICKUP_CREATE_FOLDER
Creates a new clickup folder within the specified space, which must exist and be accessible.
CLICKUP_CREATE_LIST
Creates a new list in clickup within a specified, existing folder.
CLICKUP_CREATE_TASK
Creates a new clickup task in a specific list, optionally as a subtask if a `parent` task id (which cannot be a subtask itself and must be in the same list) is provided.
CLICKUP_CREATE_TASK_COMMENT
Adds a comment to a clickup task; `team id` is required if `custom task ids` is true.
CLICKUP_DELETE_TASK
Permanently deletes a task, using its standard id or a custom task id (requires `custom task ids=true` and `team id`).
CLICKUP_GET_TASK
Retrieves comprehensive details for a clickup task by its id, supporting standard or custom task ids (requires `team id` for custom ids).
CLICKUP_UPDATE_TASK
Updates attributes of an existing task; `team id` is required if `custom task ids` is true, use a single space (" ") for `description` to clear it, and provide at least one modifiable field.
CLICKUP_AUTHORIZATION_VIEW_ACCOUNT_DETAILS
Deprecated: use `get authorized user` instead. retrieves details of the authenticated user's clickup account.
CLICKUP_CREATE_CHECKLIST
Creates a new checklist with a specified name within an existing task, which can be identified by its standard id or a custom task id (if `custom task ids` is true, `team id` is also required).
CLICKUP_CREATE_CHECKLIST_ITEM
Creates a new checklist item within a specified, existing checklist, optionally setting the item's name and assigning it to a user.
CLICKUP_CREATE_GOAL
Creates a new goal in a clickup team (workspace).
CLICKUP_CREATE_KEY_RESULT
Creates a new key result (target) for a specified goal in clickup to define and track measurable objectives towards achieving that goal.
CLICKUP_CREATE_SPACE
Creates a new space in a clickup team, with customizable name, privacy, color, and feature settings.
CLICKUP_CREATE_TEAM
Creates a new team (user group) with specified members in a workspace; member ids must be for existing users, and be aware that adding view-only guests as paid members may incur extra charges.
CLICKUP_EDIT_CHECKLIST
Updates an existing checklist's name or position, identified by its `checklist id`.
CLICKUP_FOLDERS_GET_CONTENTS_OF
Deprecated: use `get folders`. retrieves folders within a specified clickup space, ensuring `space id` is valid, with an option to filter by archived status.
CLICKUP_GET_ACCESS_TOKEN
Exchanges a clickup oauth 2.0 authorization code (obtained after user consent) for an access token.
CLICKUP_GET_GOALS
Retrieves goals for a specified clickup workspace (team); the `team id` must be valid and accessible.
CLICKUP_GET_GUEST
Call this to retrieve detailed information for a specific guest within a team (workspace), ensuring the `guest id` is valid for the given `team id`; this action requires the clickup enterprise plan.
CLICKUP_GET_LIST_MEMBERS
Retrieves all members of a specific, existing clickup list by its id.
CLICKUP_GET_TASKS
Retrieves tasks from a specified clickup list; only tasks whose home is the given list id are returned.
CLICKUP_GET_TEAMS
Retrieves user groups (teams) from a clickup workspace, typically requiring `team id` (workspace id), with an option to filter by `group ids`.
CLICKUP_GET_WORKSPACE_EVERYTHING_LEVEL_VIEWS
Retrieves all task and page views at the 'everything level' (a comprehensive overview of all tasks across all spaces) for a specified clickup workspace.
CLICKUP_TASKS_GET_TASK_DETAILS
Retrieves details for a task by its id, supporting standard or custom ids (requires `team id` for custom ids). <<deprecated: this action is deprecated. please use 'get task' instead.>>
CLICKUP_AUTHORIZATION_GET_ACCESS_TOKEN
Deprecated: use `get access token` instead. exchanges a clickup oauth 2.0 authorization code for an access token.
