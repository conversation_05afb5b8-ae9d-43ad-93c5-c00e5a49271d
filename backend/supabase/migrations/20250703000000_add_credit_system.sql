-- Add credit system to existing Atlas infrastructure
-- This migration adds token usage tracking and credit balance management

-- 1. Add credit balance tracking to existing accounts
ALTER TABLE basejump.accounts
ADD COLUMN IF NOT EXISTS credit_balance INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS monthly_credit_limit INTEGER DEFAULT 100,
ADD COLUMN IF NOT EXISTS monthly_credit_used INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS credit_reset_date TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW());

-- 2. Create token usage logs table (implements the missing backend for existing frontend)
CREATE TABLE IF NOT EXISTS token_usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    thread_id UUID NOT NULL REFERENCES threads(thread_id) ON DELETE CASCADE,
    agent_run_id UUID REFERENCES agent_runs(id) ON DELETE CASCADE,
    message_id UUID REFERENCES messages(message_id) ON DELETE CASCADE,
    model TEXT NOT NULL,
    input_tokens INTEGER NOT NULL DEFAULT 0,
    output_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    tool_calls INTEGER NOT NULL DEFAULT 0,
    duration_seconds INTEGER NOT NULL DEFAULT 0,
    credits_consumed INTEGER NOT NULL DEFAULT 0,
    estimated_cost DECIMAL(10,6) NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- 3. Create credit transactions table for purchase history
CREATE TABLE IF NOT EXISTS credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('purchase', 'consumption', 'refund', 'monthly_reset')),
    credits_amount INTEGER NOT NULL,
    credits_balance_before INTEGER NOT NULL,
    credits_balance_after INTEGER NOT NULL,
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_token_usage_logs_account_id ON token_usage_logs(account_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_logs_thread_id ON token_usage_logs(thread_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_logs_created_at ON token_usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_token_usage_logs_model ON token_usage_logs(model);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_account_id ON credit_transactions(account_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON credit_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_type ON credit_transactions(transaction_type);

-- 5. Create updated_at triggers
CREATE TRIGGER update_token_usage_logs_updated_at
    BEFORE UPDATE ON token_usage_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 6. Enable RLS on new tables
ALTER TABLE token_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policies for token_usage_logs
CREATE POLICY token_usage_logs_select_policy ON token_usage_logs
    FOR SELECT
    USING (basejump.has_role_on_account(account_id) = true);

CREATE POLICY token_usage_logs_insert_policy ON token_usage_logs
    FOR INSERT
    WITH CHECK (basejump.has_role_on_account(account_id) = true);

CREATE POLICY token_usage_logs_update_policy ON token_usage_logs
    FOR UPDATE
    USING (basejump.has_role_on_account(account_id) = true);

-- 8. Create RLS policies for credit_transactions
CREATE POLICY credit_transactions_select_policy ON credit_transactions
    FOR SELECT
    USING (basejump.has_role_on_account(account_id) = true);

CREATE POLICY credit_transactions_insert_policy ON credit_transactions
    FOR INSERT
    WITH CHECK (basejump.has_role_on_account(account_id) = true);

-- 9. Grant permissions
GRANT SELECT, INSERT, UPDATE ON TABLE token_usage_logs TO authenticated, service_role;
GRANT SELECT, INSERT ON TABLE credit_transactions TO authenticated, service_role;

-- 10. Function to record token usage and consume credits
CREATE OR REPLACE FUNCTION record_token_usage_and_consume_credits(
    p_account_id UUID,
    p_thread_id UUID,
    p_agent_run_id UUID,
    p_message_id UUID,
    p_model TEXT,
    p_input_tokens INTEGER,
    p_output_tokens INTEGER,
    p_tool_calls INTEGER DEFAULT 0,
    p_duration_seconds INTEGER DEFAULT 0
) RETURNS JSONB
SECURITY DEFINER
LANGUAGE plpgsql AS $$
DECLARE
    v_total_tokens INTEGER;
    v_credits_consumed INTEGER;
    v_estimated_cost DECIMAL(10,6);
    v_current_balance INTEGER;
    v_new_balance INTEGER;
    v_monthly_used INTEGER;
    v_usage_log_id UUID;
    v_result JSONB;
BEGIN
    -- Calculate totals
    v_total_tokens := p_input_tokens + p_output_tokens;

    -- Calculate credits using the same logic as Python function
    -- Base cost per agent run
    v_credits_consumed := 5;

    -- Token-based cost (using default rates for now)
    v_credits_consumed := v_credits_consumed + ROUND((p_input_tokens * 2.0 + p_output_tokens * 6.0) / 1000.0);

    -- Tool usage cost
    v_credits_consumed := v_credits_consumed + (p_tool_calls * 3);

    -- Duration-based cost (for long-running tasks)
    IF p_duration_seconds > 30 THEN
        v_credits_consumed := v_credits_consumed + ROUND((p_duration_seconds - 30) / 60.0 * 2);
    END IF;

    -- Minimum 1 credit per run
    v_credits_consumed := GREATEST(1, v_credits_consumed);

    -- Estimate cost (simplified - $0.01 per credit)
    v_estimated_cost := v_credits_consumed * 0.01;

    -- Get current balance and update
    SELECT credit_balance, monthly_credit_used
    INTO v_current_balance, v_monthly_used
    FROM basejump.accounts
    WHERE id = p_account_id;

    v_new_balance := v_current_balance - v_credits_consumed;

    -- Update account balance and monthly usage
    UPDATE basejump.accounts
    SET
        credit_balance = v_new_balance,
        monthly_credit_used = v_monthly_used + v_credits_consumed
    WHERE id = p_account_id;

    -- Insert usage log
    INSERT INTO token_usage_logs (
        account_id, thread_id, agent_run_id, message_id, model,
        input_tokens, output_tokens, total_tokens, tool_calls, duration_seconds,
        credits_consumed, estimated_cost
    ) VALUES (
        p_account_id, p_thread_id, p_agent_run_id, p_message_id, p_model,
        p_input_tokens, p_output_tokens, v_total_tokens, p_tool_calls, p_duration_seconds,
        v_credits_consumed, v_estimated_cost
    ) RETURNING id INTO v_usage_log_id;

    -- Insert credit transaction
    INSERT INTO credit_transactions (
        account_id, transaction_type, credits_amount,
        credits_balance_before, credits_balance_after, description
    ) VALUES (
        p_account_id, 'consumption', -v_credits_consumed,
        v_current_balance, v_new_balance,
        'Token usage for model: ' || p_model
    );

    -- Return result
    v_result := jsonb_build_object(
        'usage_log_id', v_usage_log_id,
        'credits_consumed', v_credits_consumed,
        'estimated_cost', v_estimated_cost,
        'credits_balance_before', v_current_balance,
        'credits_balance_after', v_new_balance,
        'total_tokens', v_total_tokens
    );

    RETURN v_result;
END;
$$;

-- 11. Function to add credits (for purchases)
CREATE OR REPLACE FUNCTION add_credits(
    p_account_id UUID,
    p_credits_amount INTEGER,
    p_description TEXT DEFAULT 'Credit purchase'
) RETURNS JSONB
SECURITY DEFINER
LANGUAGE plpgsql AS $$
DECLARE
    v_current_balance INTEGER;
    v_new_balance INTEGER;
    v_result JSONB;
BEGIN
    -- Get current balance
    SELECT credit_balance INTO v_current_balance
    FROM basejump.accounts
    WHERE id = p_account_id;

    v_new_balance := v_current_balance + p_credits_amount;

    -- Update balance
    UPDATE basejump.accounts
    SET credit_balance = v_new_balance
    WHERE id = p_account_id;

    -- Insert transaction
    INSERT INTO credit_transactions (
        account_id, transaction_type, credits_amount,
        credits_balance_before, credits_balance_after, description
    ) VALUES (
        p_account_id, 'purchase', p_credits_amount,
        v_current_balance, v_new_balance, p_description
    );

    v_result := jsonb_build_object(
        'credits_added', p_credits_amount,
        'credits_balance_before', v_current_balance,
        'credits_balance_after', v_new_balance
    );

    RETURN v_result;
END;
$$;

-- 12. Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION record_token_usage_and_consume_credits TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION add_credits TO authenticated, service_role;

-- 13. Initialize existing accounts with credit balances based on their subscription tier
-- This converts existing message limits to credit limits
UPDATE basejump.accounts
SET
    credit_balance = CASE
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s
            WHERE s.account_id = basejump.accounts.id
            AND s.status = 'active'
            AND s.price_id = 'price_pro_75'
        ) THEN 1500  -- Pro tier: 150 messages -> 1500 credits
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s
            WHERE s.account_id = basejump.accounts.id
            AND s.status = 'active'
            AND s.price_id = 'price_plus_35'
        ) THEN 750   -- Plus tier: 75 messages -> 750 credits
        ELSE 100     -- Free tier: 10 messages -> 100 credits
    END,
    monthly_credit_limit = CASE
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s
            WHERE s.account_id = basejump.accounts.id
            AND s.status = 'active'
            AND s.price_id = 'price_pro_75'
        ) THEN 1500
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s
            WHERE s.account_id = basejump.accounts.id
            AND s.status = 'active'
            AND s.price_id = 'price_plus_35'
        ) THEN 750
        ELSE 100
    END,
    credit_reset_date = date_trunc('month', NOW()) + interval '1 month'
WHERE credit_balance = 0;  -- Only update accounts that haven't been initialized

-- 14. Create a function to reset monthly credits (to be called by cron job)
CREATE OR REPLACE FUNCTION reset_monthly_credits() RETURNS INTEGER
SECURITY DEFINER
LANGUAGE plpgsql AS $$
DECLARE
    v_accounts_reset INTEGER := 0;
    v_account RECORD;
BEGIN
    FOR v_account IN
        SELECT id, monthly_credit_limit, credit_balance
        FROM basejump.accounts
        WHERE credit_reset_date <= NOW()
    LOOP
        -- Reset monthly usage and add monthly credits
        UPDATE basejump.accounts
        SET
            monthly_credit_used = 0,
            credit_balance = credit_balance + v_account.monthly_credit_limit,
            credit_reset_date = date_trunc('month', NOW()) + interval '1 month'
        WHERE id = v_account.id;

        -- Record transaction
        INSERT INTO credit_transactions (
            account_id, transaction_type, credits_amount,
            credits_balance_before, credits_balance_after, description
        ) VALUES (
            v_account.id, 'monthly_reset', v_account.monthly_credit_limit,
            v_account.credit_balance, v_account.credit_balance + v_account.monthly_credit_limit,
            'Monthly credit reset'
        );

        v_accounts_reset := v_accounts_reset + 1;
    END LOOP;

    RETURN v_accounts_reset;
END;
$$;

GRANT EXECUTE ON FUNCTION reset_monthly_credits TO service_role;

-- 15. Add comment for documentation
COMMENT ON TABLE token_usage_logs IS 'Tracks token usage and credit consumption for each agent run';
COMMENT ON TABLE credit_transactions IS 'Tracks credit balance changes (purchases, consumption, refunds)';
COMMENT ON COLUMN basejump.accounts.credit_balance IS 'Current available credits for the account';
COMMENT ON COLUMN basejump.accounts.monthly_credit_limit IS 'Monthly credit allowance based on subscription tier';
COMMENT ON COLUMN basejump.accounts.monthly_credit_used IS 'Credits consumed in current month';
COMMENT ON COLUMN basejump.accounts.credit_reset_date IS 'When monthly credits will be reset';
