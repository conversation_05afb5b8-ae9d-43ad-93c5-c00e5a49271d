-- Scale credit system to Manus-style larger numbers (200x multiplier)
-- This migration scales existing credit balances to maintain the same economic value

-- Update existing account credit balances to new scale (multiply by 200)
UPDATE basejump.accounts 
SET 
    credit_balance = credit_balance * 200,
    monthly_credit_limit = monthly_credit_limit * 200,
    monthly_credit_used = monthly_credit_used * 200
WHERE 
    credit_balance IS NOT NULL 
    OR monthly_credit_limit IS NOT NULL 
    OR monthly_credit_used IS NOT NULL;

-- Update the database function to use new scaled calculation
CREATE OR REPLACE FUNCTION record_token_usage_and_consume_credits(
    p_account_id UUID,
    p_thread_id UUID,
    p_agent_run_id UUID,
    p_message_id UUID,
    p_model TEXT,
    p_input_tokens INTEGER,
    p_output_tokens INTEGER,
    p_tool_calls INTEGER DEFAULT 0,
    p_duration_seconds INTEGER DEFAULT 0
) RETURNS JSONB
SECURITY DEFINER
LANGUAGE plpgsql AS $$
DECLARE
    v_total_tokens INTEGER;
    v_credits_consumed INTEGER;
    v_estimated_cost DECIMAL(10,6);
    v_current_balance INTEGER;
    v_new_balance INTEGER;
    v_monthly_used INTEGER;
    v_usage_log_id UUID;
    v_result JSONB;
    v_token_cost DECIMAL;
    v_tool_cost INTEGER;
    v_duration_cost DECIMAL;
BEGIN
    -- Calculate totals
    v_total_tokens := p_input_tokens + p_output_tokens;
    
    -- Base cost per agent run (scaled from 5 to 1000)
    v_credits_consumed := 1000;
    
    -- Token-based cost (scaled rates - default 400 input, 1200 output per 1k tokens)
    v_token_cost := (p_input_tokens * 400.0 + p_output_tokens * 1200.0) / 1000.0;
    v_credits_consumed := v_credits_consumed + ROUND(v_token_cost);
    
    -- Tool usage cost (scaled from 3 to 600)
    v_tool_cost := p_tool_calls * 600;
    v_credits_consumed := v_credits_consumed + v_tool_cost;
    
    -- Duration-based cost (scaled from 2 to 400 per minute after 30 seconds)
    IF p_duration_seconds > 30 THEN
        v_duration_cost := ((p_duration_seconds - 30) / 60.0) * 400;
        v_credits_consumed := v_credits_consumed + ROUND(v_duration_cost);
    END IF;
    
    -- Minimum 200 credits per run (scaled from 1)
    v_credits_consumed := GREATEST(200, v_credits_consumed);
    
    -- Estimate cost (simplified - $0.01 per credit, but now represents 200x fewer actual credits)
    v_estimated_cost := v_credits_consumed * 0.01;
    
    -- Get current balance and update
    SELECT credit_balance, monthly_credit_used 
    INTO v_current_balance, v_monthly_used
    FROM basejump.accounts 
    WHERE id = p_account_id;
    
    v_new_balance := v_current_balance - v_credits_consumed;
    
    -- Update account balance and monthly usage
    UPDATE basejump.accounts 
    SET 
        credit_balance = v_new_balance,
        monthly_credit_used = v_monthly_used + v_credits_consumed
    WHERE id = p_account_id;
    
    -- Insert usage log
    INSERT INTO token_usage_logs (
        account_id, thread_id, agent_run_id, message_id, model,
        input_tokens, output_tokens, total_tokens, tool_calls, duration_seconds,
        credits_consumed, estimated_cost
    ) VALUES (
        p_account_id, p_thread_id, p_agent_run_id, p_message_id, p_model,
        p_input_tokens, p_output_tokens, v_total_tokens, p_tool_calls, p_duration_seconds,
        v_credits_consumed, v_estimated_cost
    ) RETURNING id INTO v_usage_log_id;
    
    -- Insert credit transaction
    INSERT INTO credit_transactions (
        account_id, transaction_type, credits_amount, 
        credits_balance_before, credits_balance_after, description
    ) VALUES (
        p_account_id, 'consumption', -v_credits_consumed,
        v_current_balance, v_new_balance, 
        'Token usage for model: ' || p_model || ' (scaled credit system)'
    );
    
    -- Return result
    v_result := jsonb_build_object(
        'usage_log_id', v_usage_log_id,
        'credits_consumed', v_credits_consumed,
        'estimated_cost', v_estimated_cost,
        'credits_balance_before', v_current_balance,
        'credits_balance_after', v_new_balance,
        'total_tokens', v_total_tokens
    );
    
    RETURN v_result;
END;
$$;

-- Update the add_credits function for scaled amounts
CREATE OR REPLACE FUNCTION add_credits(
    p_account_id UUID,
    p_credits_amount INTEGER,
    p_description TEXT DEFAULT 'Credit purchase'
) RETURNS JSONB
SECURITY DEFINER
LANGUAGE plpgsql AS $$
DECLARE
    v_current_balance INTEGER;
    v_new_balance INTEGER;
    v_result JSONB;
BEGIN
    -- Get current balance
    SELECT credit_balance INTO v_current_balance
    FROM basejump.accounts 
    WHERE id = p_account_id;
    
    v_new_balance := v_current_balance + p_credits_amount;
    
    -- Update balance
    UPDATE basejump.accounts 
    SET credit_balance = v_new_balance
    WHERE id = p_account_id;
    
    -- Insert transaction
    INSERT INTO credit_transactions (
        account_id, transaction_type, credits_amount, 
        credits_balance_before, credits_balance_after, description
    ) VALUES (
        p_account_id, 'purchase', p_credits_amount,
        v_current_balance, v_new_balance, p_description || ' (scaled credit system)'
    );
    
    v_result := jsonb_build_object(
        'credits_added', p_credits_amount,
        'credits_balance_before', v_current_balance,
        'credits_balance_after', v_new_balance
    );
    
    RETURN v_result;
END;
$$;

-- Update monthly credit reset function for scaled amounts
CREATE OR REPLACE FUNCTION reset_monthly_credits() RETURNS INTEGER
SECURITY DEFINER
LANGUAGE plpgsql AS $$
DECLARE
    v_accounts_reset INTEGER := 0;
    v_account RECORD;
BEGIN
    FOR v_account IN 
        SELECT id, monthly_credit_limit, credit_balance 
        FROM basejump.accounts 
        WHERE credit_reset_date <= NOW()
    LOOP
        -- Reset monthly usage and add monthly credits
        UPDATE basejump.accounts 
        SET 
            monthly_credit_used = 0,
            credit_balance = credit_balance + v_account.monthly_credit_limit,
            credit_reset_date = date_trunc('month', NOW()) + interval '1 month'
        WHERE id = v_account.id;
        
        -- Record transaction
        INSERT INTO credit_transactions (
            account_id, transaction_type, credits_amount, 
            credits_balance_before, credits_balance_after, description
        ) VALUES (
            v_account.id, 'monthly_reset', v_account.monthly_credit_limit,
            v_account.credit_balance, v_account.credit_balance + v_account.monthly_credit_limit,
            'Monthly credit reset (scaled credit system)'
        );
        
        v_accounts_reset := v_accounts_reset + 1;
    END LOOP;
    
    RETURN v_accounts_reset;
END;
$$;

-- Add comment about the scaling
COMMENT ON TABLE token_usage_logs IS 'Tracks token usage and credit consumption for each agent run (scaled 200x for Manus-style numbers)';

-- Log the migration
INSERT INTO credit_transactions (
    account_id, transaction_type, credits_amount, 
    credits_balance_before, credits_balance_after, description,
    created_at
)
SELECT 
    id as account_id,
    'monthly_reset' as transaction_type,
    0 as credits_amount,
    credit_balance / 200 as credits_balance_before,  -- Show old balance
    credit_balance as credits_balance_after,         -- Show new balance
    'Credit system scaled 200x to Manus-style numbers' as description,
    NOW() as created_at
FROM basejump.accounts 
WHERE credit_balance > 0;