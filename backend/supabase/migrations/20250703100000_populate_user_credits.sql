-- Populate existing users with credits based on their actual subscription tiers
-- This uses the real price IDs from your config

-- Update existing accounts with proper credit balances
UPDATE basejump.accounts 
SET 
    credit_balance = CASE 
        -- Pro tier - $75/month (Production)
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s 
            WHERE s.account_id = basejump.accounts.id 
            AND s.status = 'active'
            AND s.price_id = 'price_1Ra0bGGKgx4qnTxJdHU2hODo'
        ) THEN 1500  -- Pro tier: 1500 credits
        
        -- Pro tier - $75/month (Staging)
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s 
            WHERE s.account_id = basejump.accounts.id 
            AND s.status = 'active'
            AND s.price_id = 'price_1Ra3k9GKgx4qnTxJzhyzEBon'
        ) THEN 1500  -- Pro tier: 1500 credits
        
        -- Plus tier - $20/month (Both prod and staging use same ID)
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s 
            WHERE s.account_id = basejump.accounts.id 
            AND s.status = 'active'
            AND s.price_id = 'price_1Rgc2vGKgx4qnTxJHslAjhLw'
        ) THEN 750   -- Plus tier: 750 credits
        
        -- Free tier (default)
        ELSE 100     -- Free tier: 100 credits
    END,
    monthly_credit_limit = CASE 
        -- Pro tier - $75/month (Production)
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s 
            WHERE s.account_id = basejump.accounts.id 
            AND s.status = 'active'
            AND s.price_id = 'price_1Ra0bGGKgx4qnTxJdHU2hODo'
        ) THEN 1500
        
        -- Pro tier - $75/month (Staging)
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s 
            WHERE s.account_id = basejump.accounts.id 
            AND s.status = 'active'
            AND s.price_id = 'price_1Ra3k9GKgx4qnTxJzhyzEBon'
        ) THEN 1500
        
        -- Plus tier - $20/month
        WHEN EXISTS (
            SELECT 1 FROM basejump.billing_subscriptions s 
            WHERE s.account_id = basejump.accounts.id 
            AND s.status = 'active'
            AND s.price_id = 'price_1Rgc2vGKgx4qnTxJHslAjhLw'
        ) THEN 750
        
        -- Free tier (default)
        ELSE 100
    END,
    monthly_credit_used = 0,  -- Reset monthly usage for migration
    credit_reset_date = date_trunc('month', NOW()) + interval '1 month'
WHERE TRUE;  -- Update all accounts

-- Record initial credit transactions for transparency
INSERT INTO credit_transactions (
    account_id, 
    transaction_type, 
    credits_amount, 
    credits_balance_before, 
    credits_balance_after, 
    description,
    created_at
)
SELECT 
    id as account_id,
    'monthly_reset' as transaction_type,
    credit_balance as credits_amount,
    0 as credits_balance_before,
    credit_balance as credits_balance_after,
    'Initial credit allocation - migration to credit system' as description,
    NOW() as created_at
FROM basejump.accounts 
WHERE credit_balance > 0;

-- Create trigger function for new user credit allocation
CREATE OR REPLACE FUNCTION allocate_free_credits_to_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Give new users 100 free credits
    NEW.credit_balance := 100;
    NEW.monthly_credit_limit := 100;
    NEW.monthly_credit_used := 0;
    NEW.credit_reset_date := date_trunc('month', NOW()) + interval '1 month';
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new accounts
DROP TRIGGER IF EXISTS trigger_allocate_credits_new_account ON basejump.accounts;
CREATE TRIGGER trigger_allocate_credits_new_account
    BEFORE INSERT ON basejump.accounts
    FOR EACH ROW
    EXECUTE FUNCTION allocate_free_credits_to_new_user();

-- Function to allocate credits when subscription changes
CREATE OR REPLACE FUNCTION allocate_subscription_credits(
    p_account_id UUID,
    p_price_id TEXT
) RETURNS JSONB
SECURITY DEFINER
LANGUAGE plpgsql AS $$
DECLARE
    v_credits_to_add INTEGER;
    v_monthly_limit INTEGER;
    v_current_balance INTEGER;
    v_new_balance INTEGER;
    v_plan_name TEXT;
    v_result JSONB;
BEGIN
    -- Determine credits and plan based on price_id
    CASE p_price_id
        -- Pro tier price IDs
        WHEN 'price_1Ra0bGGKgx4qnTxJdHU2hODo', 'price_1Ra3k9GKgx4qnTxJzhyzEBon' THEN
            v_credits_to_add := 1500;
            v_monthly_limit := 1500;
            v_plan_name := 'Pro';
        -- Plus tier price ID
        WHEN 'price_1Rgc2vGKgx4qnTxJHslAjhLw' THEN
            v_credits_to_add := 750;
            v_monthly_limit := 750;
            v_plan_name := 'Plus';
        -- Free tier or unknown
        ELSE
            v_credits_to_add := 100;
            v_monthly_limit := 100;
            v_plan_name := 'Free';
    END CASE;
    
    -- Get current balance
    SELECT credit_balance INTO v_current_balance
    FROM basejump.accounts 
    WHERE id = p_account_id;
    
    -- Calculate new balance (add monthly allocation)
    v_new_balance := v_current_balance + v_credits_to_add;
    
    -- Update account
    UPDATE basejump.accounts 
    SET 
        credit_balance = v_new_balance,
        monthly_credit_limit = v_monthly_limit,
        monthly_credit_used = 0,  -- Reset monthly usage on plan change
        credit_reset_date = date_trunc('month', NOW()) + interval '1 month'
    WHERE id = p_account_id;
    
    -- Record transaction
    INSERT INTO credit_transactions (
        account_id, transaction_type, credits_amount, 
        credits_balance_before, credits_balance_after, description
    ) VALUES (
        p_account_id, 'purchase', v_credits_to_add,
        v_current_balance, v_new_balance, 
        'Subscription upgrade to ' || v_plan_name || ' plan'
    );
    
    v_result := jsonb_build_object(
        'plan_name', v_plan_name,
        'credits_added', v_credits_to_add,
        'monthly_limit', v_monthly_limit,
        'credits_balance_before', v_current_balance,
        'credits_balance_after', v_new_balance
    );
    
    RETURN v_result;
END;
$$;

GRANT EXECUTE ON FUNCTION allocate_subscription_credits TO authenticated, service_role;